/**
 * SSE 数据处理工具类
 * 提供通用的 SSE 数据解析和处理功能
 */

export interface SSEMessage {
  messages: string[]
  remainingBuffer: string
}

export interface SSEEventData {
  event?: string
  data?: any
}

/**
 * 解析SSE数据流
 * 正确处理跨chunk的SSE消息
 */
export function parseSSEData(buffer: string): SSEMessage {
  const messages: string[] = []
  const lines = buffer.split('\n')
  let currentMessage = ''
  let i = 0

  while (i < lines.length) {
    const line = lines[i]

    // 如果是最后一行且不以换行符结尾，可能是不完整的数据
    if (i === lines.length - 1 && !buffer.endsWith('\n')) {
      return { messages, remainingBuffer: line }
    }

    // 空行表示一个SSE消息的结束
    if (line.trim() === '') {
      if (currentMessage.trim()) {
        messages.push(currentMessage.trim())
        currentMessage = ''
      }
    } else {
      currentMessage += `${line}\n`
    }

    i++
  }

  // 如果buffer以换行符结尾，处理最后一个消息
  if (buffer.endsWith('\n') && currentMessage.trim()) {
    messages.push(currentMessage.trim())
  }

  return {
    messages,
    remainingBuffer: buffer.endsWith('\n') ? '' : currentMessage,
  }
}

/**
 * 解析单个SSE消息，提取事件和数据
 */
export function parseSSEMessage(message: string): SSEEventData {
  const lines = message.split('\n')
  let event: string | undefined
  let data: any

  for (const line of lines) {
    if (line.startsWith('event:')) {
      event = line.substring(6).trim()
    } else if (line.startsWith('data:')) {
      const dataContent = line.substring(5).trim()
      if (dataContent) {
        try {
          data = JSON.parse(dataContent)
        } catch (error) {
          console.error('解析SSE数据失败:', error)
          data = dataContent
        }
      }
    }
  }

  return { event, data }
}

/**
 * 检查chunk中是否包含错误信息
 */
export function checkChunkError(chunk: string): { hasError: boolean; errorMsg?: string } {
  // 检查500错误
  if (chunk.includes('"code":500') || chunk.includes('"code": 500')) {
    try {
      const errorData = JSON.parse(chunk)
      if (errorData.code !== 200) {
        return { hasError: true, errorMsg: errorData.msg || '请求异常' }
      }
    } catch (e) {
      const errorMatch = chunk.match(/"msg"\s*:\s*"([^"]+)"/)
      const errorMsg = errorMatch ? errorMatch[1] : '服务器内部错误'
      return { hasError: true, errorMsg }
    }
  }

  // 检查401错误
  if (chunk.includes('"code":401') || chunk.includes('"code": 401')) {
    return { hasError: true, errorMsg: '未授权，请重新登录' }
  }

  return { hasError: false }
}

/**
 * 通用的SSE流处理器
 */
export class SSEStreamProcessor {
  private sseBuffer = ''
  private decoder = new TextDecoder()

  constructor(
    private onEvent: (event: string) => void,
    private onData: (data: any) => void,
    private onError: (error: string) => void,
  ) {}

  /**
   * 处理新的chunk数据
   */
  processChunk(chunk: Uint8Array): void {
    const chunkText = this.decoder.decode(chunk)

    // 检查错误
    const { hasError, errorMsg } = checkChunkError(chunkText)
    if (hasError && errorMsg) {
      this.onError(errorMsg)
      return
    }

    // 将新的chunk数据添加到缓冲区
    this.sseBuffer += chunkText

    // 解析SSE数据
    const { messages, remainingBuffer } = parseSSEData(this.sseBuffer)
    this.sseBuffer = remainingBuffer

    // 处理每个完整的SSE消息
    for (const message of messages) {
      const { event, data } = parseSSEMessage(message)

      if (event) {
        this.onEvent(event)
      }

      if (data !== undefined) {
        this.onData(data)
      }
    }
  }

  /**
   * 重置处理器状态
   */
  reset(): void {
    this.sseBuffer = ''
  }
}
